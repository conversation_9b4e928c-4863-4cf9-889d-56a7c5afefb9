#!/bin/bash

# Lấy username/password từ environment variables
MQTT_USERNAME=${MQTT_USERNAME:-admin}
MQTT_PASSWORD=${MQTT_PASSWORD:-admin123}

# Tạo file password nếu chưa tồn tại
if [ ! -f /mosquitto/config/pwfile ]; then
    echo "Creating password file..."

    # Tạo user với username/password từ environment
    mosquitto_passwd -c -b /mosquitto/config/pwfile "$MQTT_USERNAME" "$MQTT_PASSWORD"

    # Thêm user khác nếu cần
    mosquitto_passwd -b /mosquitto/config/pwfile user1 password123

    echo "Password file created successfully!"
    echo "Username: $MQTT_USERNAME"

    # Set quyền cho file
    chmod 600 /mosquitto/config/pwfile
    chown mosquitto:mosquitto /mosquitto/config/pwfile 2>/dev/null || true
else
    echo "Password file already exists."
fi

# Ch<PERSON>y mosquitto
exec /usr/sbin/mosquitto -c /mosquitto/config/mosquitto.conf
