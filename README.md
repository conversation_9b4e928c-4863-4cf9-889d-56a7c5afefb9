# MQTT Service with Docker

## Giới thiệu
MQTT Service là một dịch vụ sử dụng giao thức MQTT (Message Queuing Telemetry Transport) để truyền nhận dữ liệu giữa các thiết bị. Dự án này cung cấp môi trường MQTT đóng gói sẵn trong Docker để dễ dàng triển khai.

## Y<PERSON>u cầu hệ thống
- Docker
- Docker Compose

## Hướng dẫn cài đặt

### Phương pháp 1: Sử dụng Eclipse Mosquitto với Docker Compose (Khuyến nghị)

1. Clone repository này về máy của bạn:
```bash
git clone https://github.com/hungtx2001/mqtt-server.git
cd mqtt-server
```

2. Khởi chạy MQTT broker:
```bash
docker-compose up -d
```

### Phương pháp 2: Sử dụng Docker đơn giản

Nếu bạn không cần cấu hình phức tạp, chỉ cần ch<PERSON>y lệnh sau:

```bash
docker run -d --name mqtt-broker \
  -p 1883:1883 \
  eclipse-mosquitto:latest
```