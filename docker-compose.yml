version: "3.7"
services:
  mqtt5:
    image: eclipse-mosquitto
    container_name: mqtt5
    ports:
      - "1883:1883"
    volumes:
      - ./config:/mosquitto/config:rw
      - ./data:/mosquitto/data:rw
      - ./log:/mosquitto/log:rw
    environment:
      - MQTT_USERNAME=admin
      - MQTT_PASSWORD=admin
    entrypoint: ["/bin/sh", "/mosquitto/config/init-password.sh"]
    restart: unless-stopped

volumes:
  config:
  data:
  log:

networks:
  default:
    name: mqtt5-network
