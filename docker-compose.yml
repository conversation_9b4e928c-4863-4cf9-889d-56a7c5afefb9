version: "3.7"
services:
  mqtt5:
    image: eclipse-mosquitto
    container_name: mqtt
    ports:
      - "1883:1883"
    volumes:
      - ./config:/mosquitto/config:rw
      - ./data:/mosquitto/data:rw
      - ./log:/mosquitto/log:rw
    command: >
      sh -c "
        [ ! -f /mosquitto/config/pwfile ] && 
        mosquitto_passwd -c -b /mosquitto/config/pwfile admin admin
        /usr/sbin/mosquitto -c /mosquitto/config/mosquitto.conf
      "
    restart: unless-stopped

volumes:
  config:
  data:
  log: